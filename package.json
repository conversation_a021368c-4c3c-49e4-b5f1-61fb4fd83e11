{"name": "benefitlens", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "dev:setup": "./scripts/dev-setup.sh", "dev:start": "docker-compose up -d && npm run dev", "dev:stop": "docker-compose down", "dev:reset": "docker-compose down -v && docker-compose up -d", "dev:logs": "docker-compose logs -f", "import:german-companies": "node scripts/import-german-companies.js", "import:german-companies:dry-run": "node scripts/import-german-companies.js --dry-run", "import:dax-mdax": "node scripts/import-german-companies.js --source=dax-mdax", "import:dax-mdax:dry-run": "node scripts/import-german-companies.js --source=dax-mdax --dry-run", "import:dax": "node scripts/import-german-companies.js --source=dax-mdax --index=dax", "import:dax:dry-run": "node scripts/import-german-companies.js --source=dax-mdax --index=dax --dry-run", "import:mdax": "node scripts/import-german-companies.js --source=dax-mdax --index=mdax", "import:mdax:dry-run": "node scripts/import-german-companies.js --source=dax-mdax --index=mdax --dry-run", "import:german-benefits": "node scripts/import-german-benefits.js", "import:german-benefits:dry-run": "node scripts/import-german-benefits.js --dry-run", "assign:german-benefits": "node scripts/assign-german-benefits.js", "assign:german-benefits:dry-run": "node scripts/assign-german-benefits.js --dry-run", "import:data": "node scripts/import-data.js", "import:demo-companies": "node scripts/import-data.js --type=companies --file=data/demo-companies.csv", "import:demo-companies:dry-run": "node scripts/import-data.js --type=companies --file=data/demo-companies.csv --dry-run", "import:demo-benefits": "node scripts/import-data.js --type=benefits --file=data/demo-benefits.json", "import:demo-benefits:dry-run": "node scripts/import-data.js --type=benefits --file=data/demo-benefits.json --dry-run", "import:demo-benefits-csv": "node scripts/import-data.js --type=benefits --file=data/demo-benefits.csv", "import:demo-benefits-csv:dry-run": "node scripts/import-data.js --type=benefits --file=data/demo-benefits.csv --dry-run", "health:check": "curl -f http://localhost:3000/api/health || exit 1", "health:simple": "curl -f http://localhost:3000/api/health/simple || exit 1", "validate:env": "npx tsx src/lib/env-validation.ts", "db:backup": "pg_dump $DATABASE_URL > backups/backup_$(date +%Y%m%d_%H%M%S).sql", "db:restore": "psql $DATABASE_URL < backups/latest.sql", "logs:tail": "docker-compose logs -f", "redis:cli": "docker exec -it benefitlens-redis redis-cli", "db:cli": "docker exec -it benefitlens-postgres psql -U benefitlens_user -d benefitlens"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@types/bcryptjs": "^2.4.6", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.5", "@types/redis": "^4.0.10", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "csv-parser": "^3.2.0", "lucide-react": "^0.536.0", "next": "^15.4.6", "node-fetch": "^3.3.2", "nodemailer": "^7.0.5", "pg": "^8.16.3", "react": "19.1.1", "react-dom": "19.1.1", "redis": "^5.7.0", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "dotenv": "^17.2.1", "eslint": "^9", "eslint-config-next": "^15.4.6", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}